<?php
// Database connection settings
$host = 'localhost'; // Database host
$dbname = 'your_database_name'; // Database name
$username = 'your_username'; // Database username
$password = 'your_password'; // Database password

// Create a connection to the database
function getDbConnection() {
    global $host, $dbname, $username, $password;
    try {
        $conn = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $conn;
    } catch (PDOException $e) {
        echo "Connection failed: " . $e->getMessage();
        return null;
    }
}

// Function to insert view count
function insertViewCount($movieId, $ipAddress) {
    $conn = getDbConnection();
    if ($conn) {
        $stmt = $conn->prepare("INSERT INTO movie_views (movie_id, ip_address, view_time) VALUES (:movie_id, :ip_address, NOW())");
        $stmt->bindParam(':movie_id', $movieId);
        $stmt->bindParam(':ip_address', $ipAddress);
        $stmt->execute();
    }
}

// Function to get view count
function getViewCount($movieId) {
    $conn = getDbConnection();
    if ($conn) {
        $stmt = $conn->prepare("SELECT COUNT(*) FROM movie_views WHERE movie_id = :movie_id");
        $stmt->bindParam(':movie_id', $movieId);
        $stmt->execute();
        return $stmt->fetchColumn();
    }
    return 0;
}
?>