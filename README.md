# Movie View Counter

This project implements a movie view counter for a website, designed to work seamlessly with WordPress. It captures unique user IP addresses, prevents duplicate views, and displays the view count on the movie cover page.

## Project Structure

```
movie-view-counter
├── src
│   ├── counter.php        # Logic to capture user IP and update view count
│   ├── display.php        # Retrieves and displays the view count
│   ├── includes
│   │   ├── db.php        # Handles database connections and queries
│   │   └── ip_utils.php   # Utility functions for IP address handling
│   └── assets
│       └── style.css      # Styles for the view counter display
├── functions.php          # WordPress theme functions for enqueuing scripts/styles
└── README.md              # Project documentation
```

## Setup Instructions

1. **Clone the Repository**: 
   Clone this repository to your local machine.

2. **Database Configuration**: 
   Update the `db.php` file with your database connection details.

3. **Include in WordPress**: 
   Add the necessary includes in your `functions.php` to integrate the view counter into your WordPress theme.

4. **Styling**: 
   Customize the styles in `style.css` to match your website's design.

5. **Testing**: 
   Use the Live Server extension to test the implementation in real-time.

## Usage Guidelines

- The view counter will automatically capture and update the view count based on unique user IP addresses.
- Ensure that your server allows for the necessary database operations and that no caching plugins interfere with the view counting logic.
- Regularly check the database for view counts to ensure accuracy and performance.

## Additional Notes

- This implementation is designed to be lightweight and efficient, minimizing the impact on page load times.
- Consider implementing additional security measures to protect against potential abuse of the view counting feature.