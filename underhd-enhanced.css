/* Enhanced Modern UnderHD CSS - ปรับปรุงจาก CSS เดิม */

/* ===== ENHANCED MASTER HEADER ===== */
.master-head {
    position: relative !important;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%) !important;
    background-size: 400% 400% !important;
    animation: gradientShift 15s ease infinite !important;
    box-shadow: 0 8px 40px rgba(0, 0, 0, 0.6) !important;
    border-bottom: 1px solid rgba(255, 215, 0, 0.2) !important;
    overflow: hidden !important;
}

/* Animated Background Effects */
.master-head::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 30%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(255, 69, 0, 0.08) 0%, transparent 50%);
    animation: floatingLights 20s ease-in-out infinite;
    z-index: 1;
}

.master-head::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.03) 50%, transparent 70%);
    animation: shimmerEffect 12s linear infinite;
    z-index: 1;
}

/* ===== ENHANCED SITE BRANDING ===== */
.site-branding {
    position: relative !important;
    z-index: 10 !important;
    width: 100% !important;
}

.site-branding h1 {
    text-align: center !important;
    color: #ffd700 !important;
    font-size: clamp(1.5rem, 3vw, 2.5rem) !important;
    font-family: 'Kanit', sans-serif !important;
    font-weight: 800 !important;
    background: linear-gradient(45deg, #ffd700, #ff6b35, #f7931e, #ffd700) !important;
    background-size: 300% 300% !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    animation: titleGlow 4s ease-in-out infinite !important;
    text-shadow: 0 0 30px rgba(255, 215, 0, 0.3) !important;
    letter-spacing: 1px !important;
    margin-bottom: 20px !important;
}

/* ===== ENHANCED HEADER INFO ===== */
.header-info {
    width: 1440px !important;
    margin: 0 auto !important;
    display: grid !important;
    grid-template-columns: 15% 70% 15% !important;
    align-items: center !important;
    padding: 20px 0 30px !important;
    position: relative !important;
    z-index: 10 !important;
}

/* ===== ENHANCED HEADER LOGO ===== */
section.header-logo {
    position: relative !important;
    z-index: 10 !important;
}

section.header-logo img {
    width: 220px !important;
    object-fit: cover !important;
    float: right !important;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    filter: drop-shadow(0 4px 12px rgba(255, 215, 0, 0.3)) !important;
    border-radius: 12px !important;
}

section.header-logo img:hover {
    transform: scale(1.1) rotate(2deg) !important;
    filter: drop-shadow(0 8px 20px rgba(255, 215, 0, 0.5)) !important;
}

/* ===== ENHANCED SEARCH INPUT ===== */
.search-input {
    position: relative !important;
    z-index: 10 !important;
}

#searchform {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    position: relative !important;
}

input#input-ser {
    padding: 15px 20px !important;
    border-radius: 25px !important;
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(20px) !important;
    color: white !important;
    border: 1px solid rgba(255, 215, 0, 0.3) !important;
    margin: 0 !important;
    background-image: none !important;
    text-indent: 0 !important;
    width: 75% !important;
    font-size: 16px !important;
    outline: 0 !important;
    font-family: 'Kanit', sans-serif !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}

input#input-ser::placeholder {
    color: rgba(255, 255, 255, 0.7) !important;
    font-weight: 300 !important;
}

input#input-ser:focus {
    background: rgba(255, 255, 255, 0.15) !important;
    border-color: rgba(255, 215, 0, 0.5) !important;
    box-shadow: 0 12px 40px rgba(255, 215, 0, 0.2) !important;
}

/* Search Icon */
#searchform::after {
    content: '🔍';
    position: absolute;
    right: 15%;
    top: 50%;
    transform: translateY(-50%);
    font-size: 18px;
    opacity: 0.8;
    animation: searchPulse 2s ease-in-out infinite;
    z-index: 11;
}

/* ===== ENHANCED CONTACT BUTTON ===== */
.hd-contact a {
    background: linear-gradient(148deg, #ffd700, #ff6b35, #f7931e) !important;
    padding: 15px 30px !important;
    font-size: 16px !important;
    line-height: 1 !important;
    font-family: 'Kanit', sans-serif !important;
    font-weight: 600 !important;
    color: #000 !important;
    border-radius: 30px !important;
    display: inline-flex !important;
    align-items: center !important;
    width: 180px !important;
    height: 55px !important;
    justify-content: center !important;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    box-shadow: 0 6px 25px rgba(255, 215, 0, 0.3) !important;
    position: relative !important;
    overflow: hidden !important;
    z-index: 10 !important;
}

.hd-contact a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s;
}

.hd-contact a:hover::before {
    left: 100%;
}

.hd-contact a:hover {
    transform: translateY(-3px) scale(1.05) !important;
    box-shadow: 0 10px 35px rgba(255, 215, 0, 0.5) !important;
    color: white !important;
}

/* ===== ENHANCED MAIN NAVIGATION ===== */
.main-navigation {
    display: block !important;
    width: 100% !important;
    border-top: 1px solid rgba(255, 215, 0, 0.2) !important;
    background: rgba(255, 255, 255, 0.05) !important;
    backdrop-filter: blur(20px) !important;
    position: relative !important;
    z-index: 10 !important;
}

.main-navigation ul {
    display: block !important;
    list-style: none !important;
    margin: 0 !important;
    padding-left: 0 !important;
    text-align: center !important;
}

.menu-menu-container {
    width: 100% !important;
    text-align: center !important;
}

.main-navigation li {
    position: relative !important;
    display: inline-flex !important;
    padding: 0 1% !important;
}

.main-navigation a {
    display: block !important;
    text-decoration: none !important;
    padding: 20px 15px !important;
    font-family: 'Kanit', sans-serif !important;
    font-weight: 600 !important;
    color: rgba(255, 255, 255, 0.8) !important;
    position: relative !important;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    border-radius: 25px !important;
    margin: 5px !important;
}

.main-navigation a:before {
    content: "" !important;
    position: absolute !important;
    height: 3px !important;
    left: 20% !important;
    right: 20% !important;
    bottom: 8px !important;
    border-radius: 15px !important;
    background: linear-gradient(148deg, #ffd700, #ff6b35, #f7931e) !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
}

.main-navigation a:hover,
.main-navigation .current-menu-item a {
    color: #ffd700 !important;
    background: rgba(255, 215, 0, 0.1) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.2) !important;
}

.main-navigation a:hover:before,
.main-navigation .current-menu-item a:before {
    visibility: visible !important;
}

/* ===== CONTAINER INDEX ===== */
.container-index {
    width: 1440px !important;
    margin: 0 auto !important;
    position: relative !important;
}

/* ===== PERFECT CENTER MOVIE GRID FIX ===== */
/* Force Center All WordPress Containers */
.site,
.site-content,
#page,
#content,
.wp-site-blocks,
.wp-block-group,
.entry-content,
.content-wrapper,
.main-wrapper {
    background: #000000 !important;
    width: 100% !important;
    margin: 0 auto !important;
    padding: 0 !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
}

/* Perfect Center Movie Grid */
.movies-grid,
.movie-list,
.content-area,
.main-content,
.site-main {
    width: 100% !important;
    max-width: 1200px !important;
    margin: 0 auto !important;
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
    align-items: flex-start !important;
    gap: 20px !important;
    padding: 30px 20px !important;
    background: transparent !important;
}

/* Enhanced Movie Items */
.movie-item,
.post-item,
.film-item,
article {
    flex: 0 0 auto !important;
    margin: 0 !important;
    width: 200px !important;
    max-width: 200px !important;
    background: rgba(255, 255, 255, 0.03) !important;
    border-radius: 12px !important;
    overflow: hidden !important;
    border: 1px solid rgba(255, 107, 157, 0.1) !important;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    position: relative !important;
    backdrop-filter: blur(5px) !important;
}

.movie-item::before,
.post-item::before,
.film-item::before,
article::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 107, 157, 0.05), rgba(116, 185, 255, 0.05));
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: 1;
    border-radius: 12px;
}

/* Enhanced Movie Poster */
.movie-poster,
.post-thumbnail,
.film-poster {
    width: 200px !important;
    height: 280px !important;
    object-fit: cover !important;
    border-radius: 12px 12px 0 0 !important;
    overflow: hidden !important;
    position: relative !important;
    background: linear-gradient(135deg, #1a1a1a, #2d2d2d) !important;
}

.movie-poster img,
.post-thumbnail img,
.film-poster img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    filter: brightness(0.9) contrast(1.1) !important;
}

/* Enhanced Hover Effects */
.movie-item:hover,
.post-item:hover,
.film-item:hover,
article:hover {
    transform: translateY(-10px) scale(1.03) !important;
    box-shadow:
        0 20px 60px rgba(255, 107, 157, 0.25),
        0 10px 30px rgba(116, 185, 255, 0.15) !important;
    border-color: rgba(255, 107, 157, 0.4) !important;
    z-index: 10 !important;
}

.movie-item:hover::before,
.post-item:hover::before,
.film-item:hover::before,
article:hover::before {
    opacity: 1;
}

.movie-item:hover .movie-poster img,
.post-item:hover .post-thumbnail img,
.film-item:hover .film-poster img {
    transform: scale(1.15) rotate(1deg) !important;
    filter: brightness(1.2) contrast(1.3) saturate(1.1) !important;
}

/* Enhanced Movie Info */
.movie-info,
.post-info,
.film-info {
    padding: 15px !important;
    text-align: center !important;
    position: relative !important;
    z-index: 2 !important;
    background: rgba(0, 0, 0, 0.7) !important;
    backdrop-filter: blur(10px) !important;
}

.movie-title,
.post-title,
.film-title {
    color: rgba(255, 255, 255, 0.9) !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    margin-bottom: 8px !important;
    line-height: 1.3 !important;
    transition: all 0.3s ease !important;
    font-family: 'Kanit', sans-serif !important;
}

.movie-item:hover .movie-title,
.post-item:hover .post-title,
.film-item:hover .film-title {
    color: #ff6b9d !important;
    text-shadow: 0 0 10px rgba(255, 107, 157, 0.5) !important;
}

/* Enhanced Quality Tags */
.quality-tag,
.movie-quality,
.post-quality {
    position: absolute !important;
    top: 10px !important;
    left: 10px !important;
    background: linear-gradient(135deg, rgba(255, 107, 157, 0.9), rgba(196, 69, 105, 0.9)) !important;
    color: white !important;
    padding: 6px 10px !important;
    border-radius: 8px !important;
    font-size: 11px !important;
    font-weight: 700 !important;
    z-index: 5 !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
}

/* Enhanced Rating */
.movie-rating,
.post-rating,
.film-rating {
    background: linear-gradient(135deg, #ff6b9d, #74b9ff) !important;
    color: white !important;
    padding: 4px 8px !important;
    border-radius: 10px !important;
    font-size: 11px !important;
    font-weight: 700 !important;
    display: inline-block !important;
    margin-top: 5px !important;
    box-shadow: 0 2px 8px rgba(255, 107, 157, 0.3) !important;
}

/* ===== ANIMATIONS ===== */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes floatingLights {
    0%, 100% { opacity: 1; transform: translateY(0px); }
    50% { opacity: 0.7; transform: translateY(-10px); }
}

@keyframes shimmerEffect {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

@keyframes titleGlow {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes searchPulse {
    0%, 100% { opacity: 0.8; transform: translateY(-50%) scale(1); }
    50% { opacity: 1; transform: translateY(-50%) scale(1.1); }
}

/* ===== RESPONSIVE DESIGN ===== */
@media screen and (max-width: 1440px) {
    .header-info {
        width: 100% !important;
        padding: 20px !important;
    }

    .container-index {
        width: 100% !important;
        padding: 0 20px !important;
    }

    .movies-grid,
    .movie-list,
    .content-area,
    .main-content,
    .site-main {
        max-width: 100% !important;
        padding: 30px 20px !important;
    }
}

@media screen and (max-width: 1200px) {
    .site-header {
        width: 100% !important;
    }

    .header-info {
        grid-template-columns: 1fr !important;
        text-align: center !important;
        gap: 20px !important;
    }

    section.header-logo img {
        float: none !important;
        margin: 0 auto !important;
        display: block !important;
    }

    .movies-grid,
    .movie-list,
    .content-area,
    .main-content,
    .site-main {
        gap: 18px !important;
    }

    .movie-item,
    .post-item,
    .film-item,
    article {
        width: 180px !important;
        max-width: 180px !important;
    }

    .movie-poster,
    .post-thumbnail,
    .film-poster {
        width: 180px !important;
        height: 250px !important;
    }
}

@media screen and (max-width: 768px) {
    .main-navigation li {
        display: block !important;
        padding: 5px 0 !important;
    }

    .main-navigation a {
        margin: 2px 10px !important;
        padding: 15px 20px !important;
    }

    input#input-ser {
        width: 90% !important;
        font-size: 14px !important;
    }

    .hd-contact a {
        width: 150px !important;
        height: 45px !important;
        font-size: 14px !important;
    }

    .movies-grid,
    .movie-list,
    .content-area,
    .main-content,
    .site-main {
        gap: 15px !important;
        padding: 20px 15px !important;
    }

    .movie-item,
    .post-item,
    .film-item,
    article {
        width: 160px !important;
        max-width: 160px !important;
    }

    .movie-poster,
    .post-thumbnail,
    .film-poster {
        width: 160px !important;
        height: 220px !important;
    }

    .movie-title,
    .post-title,
    .film-title {
        font-size: 13px !important;
    }
}

@media screen and (max-width: 480px) {
    .movies-grid,
    .movie-list,
    .content-area,
    .main-content,
    .site-main {
        gap: 12px !important;
    }

    .movie-item,
    .post-item,
    .film-item,
    article {
        width: 140px !important;
        max-width: 140px !important;
    }

    .movie-poster,
    .post-thumbnail,
    .film-poster {
        width: 140px !important;
        height: 190px !important;
    }
}
