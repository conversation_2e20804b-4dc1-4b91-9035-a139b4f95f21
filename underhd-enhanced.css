/* Enhanced Modern UnderHD CSS - ปรับปรุงจาก CSS เดิม */

/* ===== ENHANCED MASTER HEADER ===== */
.master-head {
    position: relative !important;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%) !important;
    background-size: 400% 400% !important;
    animation: gradientShift 15s ease infinite !important;
    box-shadow: 0 8px 40px rgba(0, 0, 0, 0.6) !important;
    border-bottom: 1px solid rgba(255, 215, 0, 0.2) !important;
    overflow: hidden !important;
}

/* Animated Background Effects */
.master-head::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 30%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(255, 69, 0, 0.08) 0%, transparent 50%);
    animation: floatingLights 20s ease-in-out infinite;
    z-index: 1;
}

.master-head::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.03) 50%, transparent 70%);
    animation: shimmerEffect 12s linear infinite;
    z-index: 1;
}

/* ===== ENHANCED SITE BRANDING ===== */
.site-branding {
    position: relative !important;
    z-index: 10 !important;
    width: 100% !important;
}

.site-branding h1 {
    text-align: center !important;
    color: #ffd700 !important;
    font-size: clamp(1.5rem, 3vw, 2.5rem) !important;
    font-family: 'Kanit', sans-serif !important;
    font-weight: 800 !important;
    background: linear-gradient(45deg, #ffd700, #ff6b35, #f7931e, #ffd700) !important;
    background-size: 300% 300% !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    animation: titleGlow 4s ease-in-out infinite !important;
    text-shadow: 0 0 30px rgba(255, 215, 0, 0.3) !important;
    letter-spacing: 1px !important;
    margin-bottom: 20px !important;
}

/* ===== ENHANCED HEADER INFO ===== */
.header-info {
    width: 1440px !important;
    margin: 0 auto !important;
    display: grid !important;
    grid-template-columns: 15% 70% 15% !important;
    align-items: center !important;
    padding: 20px 0 30px !important;
    position: relative !important;
    z-index: 10 !important;
}

/* ===== ENHANCED HEADER LOGO ===== */
section.header-logo {
    position: relative !important;
    z-index: 10 !important;
}

section.header-logo img {
    width: 220px !important;
    object-fit: cover !important;
    float: right !important;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    filter: drop-shadow(0 4px 12px rgba(255, 215, 0, 0.3)) !important;
    border-radius: 12px !important;
}

section.header-logo img:hover {
    transform: scale(1.1) rotate(2deg) !important;
    filter: drop-shadow(0 8px 20px rgba(255, 215, 0, 0.5)) !important;
}

/* ===== ENHANCED SEARCH INPUT ===== */
.search-input {
    position: relative !important;
    z-index: 10 !important;
}

#searchform {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    position: relative !important;
}

input#input-ser {
    padding: 15px 20px !important;
    border-radius: 25px !important;
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(20px) !important;
    color: white !important;
    border: 1px solid rgba(255, 215, 0, 0.3) !important;
    margin: 0 !important;
    background-image: none !important;
    text-indent: 0 !important;
    width: 75% !important;
    font-size: 16px !important;
    outline: 0 !important;
    font-family: 'Kanit', sans-serif !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}

input#input-ser::placeholder {
    color: rgba(255, 255, 255, 0.7) !important;
    font-weight: 300 !important;
}

input#input-ser:focus {
    background: rgba(255, 255, 255, 0.15) !important;
    border-color: rgba(255, 215, 0, 0.5) !important;
    box-shadow: 0 12px 40px rgba(255, 215, 0, 0.2) !important;
}

/* Search Icon */
#searchform::after {
    content: '🔍';
    position: absolute;
    right: 15%;
    top: 50%;
    transform: translateY(-50%);
    font-size: 18px;
    opacity: 0.8;
    animation: searchPulse 2s ease-in-out infinite;
    z-index: 11;
}

/* ===== ENHANCED CONTACT BUTTON ===== */
.hd-contact a {
    background: linear-gradient(148deg, #ffd700, #ff6b35, #f7931e) !important;
    padding: 15px 30px !important;
    font-size: 16px !important;
    line-height: 1 !important;
    font-family: 'Kanit', sans-serif !important;
    font-weight: 600 !important;
    color: #000 !important;
    border-radius: 30px !important;
    display: inline-flex !important;
    align-items: center !important;
    width: 180px !important;
    height: 55px !important;
    justify-content: center !important;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    box-shadow: 0 6px 25px rgba(255, 215, 0, 0.3) !important;
    position: relative !important;
    overflow: hidden !important;
    z-index: 10 !important;
}

.hd-contact a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s;
}

.hd-contact a:hover::before {
    left: 100%;
}

.hd-contact a:hover {
    transform: translateY(-3px) scale(1.05) !important;
    box-shadow: 0 10px 35px rgba(255, 215, 0, 0.5) !important;
    color: white !important;
}

/* ===== ENHANCED MAIN NAVIGATION ===== */
.main-navigation {
    display: block !important;
    width: 100% !important;
    border-top: 1px solid rgba(255, 215, 0, 0.2) !important;
    background: rgba(255, 255, 255, 0.05) !important;
    backdrop-filter: blur(20px) !important;
    position: relative !important;
    z-index: 10 !important;
}

.main-navigation ul {
    display: block !important;
    list-style: none !important;
    margin: 0 !important;
    padding-left: 0 !important;
    text-align: center !important;
}

.menu-menu-container {
    width: 100% !important;
    text-align: center !important;
}

.main-navigation li {
    position: relative !important;
    display: inline-flex !important;
    padding: 0 1% !important;
}

.main-navigation a {
    display: block !important;
    text-decoration: none !important;
    padding: 20px 15px !important;
    font-family: 'Kanit', sans-serif !important;
    font-weight: 600 !important;
    color: rgba(255, 255, 255, 0.8) !important;
    position: relative !important;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    border-radius: 25px !important;
    margin: 5px !important;
}

.main-navigation a:before {
    content: "" !important;
    position: absolute !important;
    height: 3px !important;
    left: 20% !important;
    right: 20% !important;
    bottom: 8px !important;
    border-radius: 15px !important;
    background: linear-gradient(148deg, #ffd700, #ff6b35, #f7931e) !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
}

.main-navigation a:hover,
.main-navigation .current-menu-item a {
    color: #ffd700 !important;
    background: rgba(255, 215, 0, 0.1) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.2) !important;
}

.main-navigation a:hover:before,
.main-navigation .current-menu-item a:before {
    visibility: visible !important;
}

/* ===== CONTAINER INDEX ===== */
.container-index {
    width: 1440px !important;
    margin: 0 auto !important;
    position: relative !important;
}

/* ===== ANIMATIONS ===== */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes floatingLights {
    0%, 100% { opacity: 1; transform: translateY(0px); }
    50% { opacity: 0.7; transform: translateY(-10px); }
}

@keyframes shimmerEffect {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

@keyframes titleGlow {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes searchPulse {
    0%, 100% { opacity: 0.8; transform: translateY(-50%) scale(1); }
    50% { opacity: 1; transform: translateY(-50%) scale(1.1); }
}

/* ===== RESPONSIVE DESIGN ===== */
@media screen and (max-width: 1440px) {
    .header-info {
        width: 100% !important;
        padding: 20px !important;
    }
    
    .container-index {
        width: 100% !important;
        padding: 0 20px !important;
    }
}

@media screen and (max-width: 1200px) {
    .site-header {
        width: 100% !important;
    }
    
    .header-info {
        grid-template-columns: 1fr !important;
        text-align: center !important;
        gap: 20px !important;
    }
    
    section.header-logo img {
        float: none !important;
        margin: 0 auto !important;
        display: block !important;
    }
}

@media screen and (max-width: 768px) {
    .main-navigation li {
        display: block !important;
        padding: 5px 0 !important;
    }
    
    .main-navigation a {
        margin: 2px 10px !important;
        padding: 15px 20px !important;
    }
    
    input#input-ser {
        width: 90% !important;
        font-size: 14px !important;
    }
    
    .hd-contact a {
        width: 150px !important;
        height: 45px !important;
        font-size: 14px !important;
    }
}
