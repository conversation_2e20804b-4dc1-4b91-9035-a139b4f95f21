<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎬 Ultra Modern Movie Header - 2024</title>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Kanit:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    
    <!-- Modern Header CSS -->
    <link rel="stylesheet" href="src/assets/modern-header.css">
    
    <style>
        /* Additional Demo Styles */
        .demo-section {
            padding: 60px 20px;
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }
        
        .demo-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
        }
        
        .demo-subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 40px;
            line-height: 1.6;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }
        
        .feature-card {
            background: white;
            padding: 30px 25px;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: 1px solid rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }
        
        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover::before {
            transform: scaleX(1);
        }
        
        .feature-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }
        
        .feature-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .feature-desc {
            color: #666;
            line-height: 1.6;
            font-size: 0.95rem;
        }
        
        .tech-stack {
            margin-top: 60px;
            padding: 40px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 20px;
            border-left: 5px solid #4ecdc4;
        }
        
        .tech-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 20px;
        }
        
        .tech-list {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px;
        }
        
        .tech-item {
            background: white;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 500;
            color: #4ecdc4;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(78, 205, 196, 0.2);
        }
        
        .cta-section {
            margin-top: 60px;
            padding: 40px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 20px;
            color: white;
            text-align: center;
        }
        
        .cta-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 15px;
        }
        
        .cta-desc {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }
        
        .cta-button {
            display: inline-block;
            padding: 15px 35px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            font-size: 1.1rem;
        }
        
        .cta-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        /* Floating Elements */
        .floating-element {
            position: fixed;
            pointer-events: none;
            z-index: -1;
            opacity: 0.1;
            animation: float 20s ease-in-out infinite;
        }
        
        .floating-1 {
            top: 20%;
            left: 10%;
            font-size: 4rem;
            animation-delay: 0s;
        }
        
        .floating-2 {
            top: 60%;
            right: 15%;
            font-size: 3rem;
            animation-delay: -7s;
        }
        
        .floating-3 {
            bottom: 30%;
            left: 20%;
            font-size: 2.5rem;
            animation-delay: -14s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-30px) rotate(120deg); }
            66% { transform: translateY(15px) rotate(240deg); }
        }
        
        @media (max-width: 768px) {
            .demo-title { font-size: 2rem; }
            .features-grid { grid-template-columns: 1fr; }
            .tech-list { justify-content: center; }
            .floating-element { display: none; }
        }
    </style>
</head>
<body>
    <!-- Floating Background Elements -->
    <div class="floating-element floating-1">🎬</div>
    <div class="floating-element floating-2">🌟</div>
    <div class="floating-element floating-3">🎭</div>
    
    <div id="page">
        <!-- Ultra Modern Header -->
        <header class="master-head">
            <div class="site-branding">
                <div class="header-info">
                    <section class="header-logo">
                        <a href="#">
                            <img src="https://via.placeholder.com/140x70/ffffff/4ecdc4?text=🎬+MOVIE" alt="Movie Logo" style="height: 70px; border-radius: 10px;">
                        </a>
                    </section>
                    <section class="header-description">
                        <h1>🎬 Movie View Counter</h1>
                        <div class="search-input">
                            <form role="search" method="GET" action="#">
                                <input type="text" placeholder="ค้นหาหนังที่คุณชอบ..." name="s">
                            </form>
                        </div>
                    </section>
                    <section class="contact">
                        <div class="hd-contact">
                            <a href="#">📞 ติดต่อเรา</a>
                        </div>
                    </section>
                </div>
            </div>
        </header>
        
        <!-- Demo Content -->
        <main class="demo-section">
            <h2 class="demo-title">✨ Ultra Modern Header Design 2024</h2>
            <p class="demo-subtitle">
                ออกแบบ header ใหม่ล่าสุดด้วยเทคโนโลยี CSS ทันสมัย พร้อมเอฟเฟกต์สวยงามและการตอบสนองที่ลื่นไหล<br>
                เหมาะสำหรับเว็บไซต์หนังและบันเทิงในยุคดิจิทัล
            </p>
            
            <div class="features-grid">
                <div class="feature-card">
                    <span class="feature-icon">🌈</span>
                    <h3 class="feature-title">Dynamic Gradient</h3>
                    <p class="feature-desc">พื้นหลัง gradient แบบเคลื่อนไหวด้วยสีสันสดใส ทำให้ดูทันสมัยและน่าสนใจ</p>
                </div>
                
                <div class="feature-card">
                    <span class="feature-icon">💎</span>
                    <h3 class="feature-title">Glass Morphism</h3>
                    <p class="feature-desc">เอฟเฟกต์แก้วใสและ backdrop blur ที่ทำให้องค์ประกอบดูโปร่งใสและหรูหรา</p>
                </div>
                
                <div class="feature-card">
                    <span class="feature-icon">⚡</span>
                    <h3 class="feature-title">Smooth Animations</h3>
                    <p class="feature-desc">การเคลื่อนไหวที่นุ่มนวลด้วย CSS animations และ transitions ที่ปรับแต่งอย่างละเอียด</p>
                </div>
                
                <div class="feature-card">
                    <span class="feature-icon">📱</span>
                    <h3 class="feature-title">Fully Responsive</h3>
                    <p class="feature-desc">รองรับทุกขนาดหน้าจอ จากมือถือไปจนถึงเดสก์ท็อป ด้วย CSS Grid และ Flexbox</p>
                </div>
                
                <div class="feature-card">
                    <span class="feature-icon">🎨</span>
                    <h3 class="feature-title">Modern Typography</h3>
                    <p class="feature-desc">ใช้ฟอนต์ Kanit และเทคนิค text gradient ทำให้ข้อความดูสวยงามและอ่านง่าย</p>
                </div>
                
                <div class="feature-card">
                    <span class="feature-icon">🚀</span>
                    <h3 class="feature-title">Performance Optimized</h3>
                    <p class="feature-desc">เขียนโค้ดให้มีประสิทธิภาพสูง โหลดเร็ว และใช้ทรัพยากรน้อย</p>
                </div>
            </div>
            
            <div class="tech-stack">
                <h3 class="tech-title">🛠️ เทคโนโลยีที่ใช้</h3>
                <div class="tech-list">
                    <span class="tech-item">CSS Grid</span>
                    <span class="tech-item">Flexbox</span>
                    <span class="tech-item">CSS Animations</span>
                    <span class="tech-item">Backdrop Filter</span>
                    <span class="tech-item">CSS Gradients</span>
                    <span class="tech-item">Responsive Design</span>
                    <span class="tech-item">Google Fonts</span>
                    <span class="tech-item">Modern CSS</span>
                </div>
            </div>
            
            <div class="cta-section">
                <h3 class="cta-title">🎉 พร้อมใช้งานแล้ว!</h3>
                <p class="cta-desc">นำโค้ด CSS ไปใช้ในเว็บไซต์ WordPress ของคุณได้ทันที</p>
                <a href="#" class="cta-button">🚀 เริ่มใช้งาน</a>
            </div>
        </main>
    </div>
</body>
</html>
