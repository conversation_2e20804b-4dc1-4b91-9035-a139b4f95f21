<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
    <title>Under-HD | ดูหนังออนไลน์ HD เต็มเรื่อง ครบจบ 2025</title>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, maximum-scale=3.0, minimum-scale=0.3">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎬</text></svg>" type="image/x-icon">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Kanit:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    
    <!-- Modern UnderHD CSS -->
    <link rel="stylesheet" href="underhd-modern-style.css">
    
    <style>
        /* Demo Content Styles */
        .demo-content {
            background: #0a0a0a;
            min-height: 100vh;
            padding: 40px 20px;
        }
        
        .movies-section {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .section-title {
            color: white;
            font-size: 2rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 30px;
            background: linear-gradient(45deg, #ffd700, #ff6b35);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .movies-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }
        
        /* Enhanced Movie Cards with Smooth Hover */
        .movie-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
        }
        
        .movie-item:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 60px rgba(255, 215, 0, 0.3);
            border-color: rgba(255, 215, 0, 0.5);
        }
        
        .movie-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(255, 215, 0, 0.1), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
        }
        
        .movie-item:hover::before {
            opacity: 1;
        }
        
        .movie-poster {
            position: relative;
            height: 280px;
            background: linear-gradient(45deg, #333, #555);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 3rem;
            overflow: hidden;
            border-radius: 12px 12px 0 0;
        }
        
        .movie-poster img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            filter: brightness(0.9) contrast(1.1);
        }
        
        .movie-item:hover .movie-poster img {
            transform: scale(1.1);
            filter: brightness(1.1) contrast(1.2);
        }
        
        .movie-info {
            padding: 20px;
            position: relative;
            z-index: 2;
        }
        
        .movie-title {
            color: white;
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 10px;
            line-height: 1.3;
            transition: color 0.3s ease;
        }
        
        .movie-item:hover .movie-title {
            color: #ffd700;
        }
        
        .movie-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .movie-year {
            color: #888;
            font-size: 0.85rem;
        }
        
        .movie-rating {
            background: linear-gradient(45deg, #ffd700, #ff6b35);
            color: white;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
        }
        
        .movie-genre {
            color: #aaa;
            font-size: 0.85rem;
            transition: color 0.3s ease;
        }
        
        .movie-item:hover .movie-genre {
            color: #ccc;
        }
        
        .quality-tag {
            position: absolute;
            top: 12px;
            left: 12px;
            background: rgba(255, 107, 53, 0.9);
            color: white;
            padding: 6px 12px;
            border-radius: 10px;
            font-size: 0.75rem;
            font-weight: 600;
            backdrop-filter: blur(10px);
            z-index: 3;
        }
        
        /* Modern Menu Demo */
        .demo-menu {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(20px);
            border-radius: 50px;
            padding: 8px;
            margin-top: 20px;
            border: 1px solid rgba(255, 215, 0, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .demo-menu ul {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 5px;
            list-style: none;
            flex-wrap: wrap;
        }
        
        .demo-menu a {
            display: block;
            padding: 12px 24px;
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            border-radius: 25px;
            font-weight: 500;
            font-size: 14px;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
        }
        
        .demo-menu a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .demo-menu a:hover::before {
            left: 100%;
        }
        
        .demo-menu a:hover {
            background: rgba(255, 215, 0, 0.2);
            color: #ffd700;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 215, 0, 0.3);
        }
        
        @media (max-width: 768px) {
            .movies-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                gap: 20px;
            }
            
            .movie-poster {
                height: 220px;
                font-size: 2rem;
            }
            
            .demo-menu ul {
                flex-direction: column;
                gap: 8px;
            }
        }
    </style>
</head>

<body class="home">
    <div id="page">
        <header class="master-head">
            <div class="site-branding">
                <div class="header-info">
                    <section class="header-logo">
                        <a href="#">
                            <img src="https://via.placeholder.com/150x75/ffd700/1a1a2e?text=🎬+Under-HD" alt="Under-HD Logo" style="height: 75px; border-radius: 10px;">
                        </a>
                    </section>
                    <section class="header-description">
                        <h1>Under-HD ดูหนังออนไลน์ HD เต็มเรื่อง ครบจบ 2025</h1>
                        <div class="search-input">
                            <form role="search" method="GET" id="searchform" action="#">
                                <input type="text" value="" placeholder="ค้นหาหนัง, ซีรี่ย์, การ์ตูน..." name="s" id="input-ser">
                            </form>
                        </div>
                    </section>
                    <section class="contact">
                        <div class="hd-contact">
                            <a href="#">📞 ติดต่อเรา</a>
                        </div>
                    </section>
                </div>
            </div>
            
            <!-- Modern Menu Demo -->
            <nav class="demo-menu">
                <ul>
                    <li><a href="#">🏠 หน้าแรก</a></li>
                    <li><a href="#">🎬 หนังใหม่</a></li>
                    <li><a href="#">🔥 หนังฮิต</a></li>
                    <li><a href="#">🎭 ซีรี่ย์</a></li>
                    <li><a href="#">🌟 การ์ตูน</a></li>
                    <li><a href="#">📺 Netflix</a></li>
                    <li><a href="#">🎪 Disney+</a></li>
                </ul>
            </nav>
        </header>
        
        <!-- Demo Content -->
        <main class="demo-content">
            <div class="movies-section">
                <h2 class="section-title">🎬 หนังใหม่ล่าสุด 2025</h2>
                
                <div class="movies-grid">
                    <!-- Sample Movie Cards with Smooth Hover Effects -->
                    <div class="movie-item">
                        <div class="movie-poster">
                            <span class="quality-tag">HD</span>
                            🎬
                        </div>
                        <div class="movie-info">
                            <h3 class="movie-title">Mulan (2025) รักนี้ไม่มีวันจบ</h3>
                            <div class="movie-meta">
                                <span class="movie-year">2025</span>
                                <span class="movie-rating">⭐ 8.3</span>
                            </div>
                            <p class="movie-genre">แอคชั่น, ผจญภัย</p>
                        </div>
                    </div>
                    
                    <div class="movie-item">
                        <div class="movie-poster">
                            <span class="quality-tag">4K</span>
                            🎭
                        </div>
                        <div class="movie-info">
                            <h3 class="movie-title">Chhava (2025) ชาวา นักรบผู้กล้า</h3>
                            <div class="movie-meta">
                                <span class="movie-year">2025</span>
                                <span class="movie-rating">⭐ 7.3</span>
                            </div>
                            <p class="movie-genre">ดราม่า, ประวัติศาสตร์</p>
                        </div>
                    </div>
                    
                    <div class="movie-item">
                        <div class="movie-poster">
                            <span class="quality-tag">HD</span>
                            🏔️
                        </div>
                        <div class="movie-info">
                            <h3 class="movie-title">Secret: Untold Melody (2025)</h3>
                            <div class="movie-meta">
                                <span class="movie-year">2025</span>
                                <span class="movie-rating">⭐ 6.9</span>
                            </div>
                            <p class="movie-genre">ลึกลับ, ระทึกขวัญ</p>
                        </div>
                    </div>
                    
                    <div class="movie-item">
                        <div class="movie-poster">
                            <span class="quality-tag">HD</span>
                            ⛰️
                        </div>
                        <div class="movie-info">
                            <h3 class="movie-title">Fountain Of Youth (2025)</h3>
                            <div class="movie-meta">
                                <span class="movie-year">2025</span>
                                <span class="movie-rating">⭐ 5.7</span>
                            </div>
                            <p class="movie-genre">ผจญภัย, แฟนตาซี</p>
                        </div>
                    </div>
                    
                    <div class="movie-item">
                        <div class="movie-poster">
                            <span class="quality-tag">HD</span>
                            💕
                        </div>
                        <div class="movie-info">
                            <h3 class="movie-title">Everything About My Wife (2025)</h3>
                            <div class="movie-meta">
                                <span class="movie-year">2025</span>
                                <span class="movie-rating">⭐ 3.0</span>
                            </div>
                            <p class="movie-genre">โรแมนติก, คอมเมดี้</p>
                        </div>
                    </div>
                    
                    <div class="movie-item">
                        <div class="movie-poster">
                            <span class="quality-tag">4K</span>
                            ⚔️
                        </div>
                        <div class="movie-info">
                            <h3 class="movie-title">Battle Through The Heavens 4 (2025)</h3>
                            <div class="movie-meta">
                                <span class="movie-year">2025</span>
                                <span class="movie-rating">⭐ 7.1</span>
                            </div>
                            <p class="movie-genre">แอนิเมชั่น, แฟนตาซี</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
