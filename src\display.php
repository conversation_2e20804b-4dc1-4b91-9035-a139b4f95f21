<?php
// Include database connection and IP utility functions
include_once 'includes/db.php';
include_once 'includes/ip_utils.php';

// Function to retrieve and display the view count for a specific movie
function display_view_count($movie_id) {
    // Get the view count from the database
    $view_count = get_view_count($movie_id);
    
    // Display the view count
    echo '<div class="view-counter">';
    echo '<p>This movie has been viewed <strong>' . htmlspecialchars($view_count) . '</strong> times.</p>';
    echo '</div>';
}

// Call the function to display the view count for a specific movie
if (isset($_GET['movie_id'])) {
    $movie_id = intval($_GET['movie_id']);
    display_view_count($movie_id);
}
?>