/* Modern Movie Website Header - Under-HD Style */

/* Reset and Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Kanit', sans-serif;
    background: #0a0a0a;
    color: white;
}

/* Modern Movie Header */
.movie-header {
    position: relative;
    background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
    background-size: 400% 400%;
    animation: gradientFlow 12s ease infinite;
    padding: 20px 0;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.5);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Animated Background Effects */
.movie-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 30%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(255, 69, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(138, 43, 226, 0.05) 0%, transparent 50%);
    animation: movieGlow 15s ease-in-out infinite;
}

.movie-header::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.02) 50%, transparent 70%);
    animation: cinemaShine 10s linear infinite;
}

/* Header Container */
.header-container {
    position: relative;
    z-index: 10;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Main Title Section */
.main-title {
    text-align: center;
    margin-bottom: 25px;
}

.site-title {
    font-size: clamp(1.8rem, 4vw, 3.5rem);
    font-weight: 800;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #ffd700, #ff6b35, #f7931e, #ffd700);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: titleShimmer 4s ease-in-out infinite;
    text-shadow: 0 0 30px rgba(255, 215, 0, 0.3);
    letter-spacing: 1px;
    position: relative;
}

.site-title::before {
    content: '🎬';
    position: absolute;
    left: -50px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 2rem;
    animation: movieIcon 3s ease-in-out infinite;
}

.site-title::after {
    content: '🎭';
    position: absolute;
    right: -50px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 2rem;
    animation: movieIcon 3s ease-in-out infinite reverse;
}

.site-subtitle {
    font-size: clamp(0.9rem, 2vw, 1.2rem);
    color: rgba(255, 255, 255, 0.8);
    font-weight: 300;
    letter-spacing: 0.5px;
}

/* Navigation Menu */
.main-nav {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 30px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.nav-item {
    position: relative;
    padding: 12px 24px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    color: white;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    backdrop-filter: blur(10px);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow: hidden;
}

.nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.2), transparent);
    transition: left 0.5s;
}

.nav-item:hover::before {
    left: 100%;
}

.nav-item:hover {
    background: rgba(255, 215, 0, 0.2);
    border-color: rgba(255, 215, 0, 0.4);
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
    color: #ffd700;
}

/* Search Section */
.search-section {
    display: flex;
    justify-content: center;
    margin-top: 25px;
}

.search-container {
    position: relative;
    max-width: 500px;
    width: 100%;
}

.search-input {
    width: 100%;
    padding: 15px 50px 15px 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 30px;
    color: white;
    font-size: 16px;
    font-family: 'Kanit', sans-serif;
    backdrop-filter: blur(15px);
    transition: all 0.3s ease;
    outline: none;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.search-input:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 215, 0, 0.5);
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.2);
}

.search-btn {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: linear-gradient(45deg, #ffd700, #ff6b35);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    color: white;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-btn:hover {
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
}

/* Quality Badges */
.quality-badges {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.quality-badge {
    padding: 6px 12px;
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    color: white;
    box-shadow: 0 2px 10px rgba(255, 107, 53, 0.3);
    animation: badgePulse 2s ease-in-out infinite alternate;
}

/* Animations */
@keyframes gradientFlow {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes movieGlow {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes cinemaShine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

@keyframes titleShimmer {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes movieIcon {
    0%, 100% { transform: translateY(-50%) scale(1); }
    50% { transform: translateY(-50%) scale(1.2); }
}

@keyframes badgePulse {
    0% { transform: scale(1); }
    100% { transform: scale(1.05); }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-nav {
        gap: 20px;
    }
    
    .site-title::before,
    .site-title::after {
        display: none;
    }
}

@media (max-width: 768px) {
    .movie-header {
        padding: 15px 0;
    }
    
    .header-container {
        padding: 0 15px;
    }
    
    .main-nav {
        gap: 15px;
    }
    
    .nav-item {
        padding: 10px 18px;
        font-size: 0.9rem;
    }
    
    .search-input {
        padding: 12px 45px 12px 18px;
        font-size: 15px;
    }
    
    .quality-badges {
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .main-nav {
        flex-direction: column;
        gap: 10px;
    }
    
    .nav-item {
        width: 100%;
        text-align: center;
        max-width: 200px;
    }
    
    .quality-badges {
        justify-content: center;
    }
}
