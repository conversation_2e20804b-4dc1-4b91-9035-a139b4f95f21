<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
    <title>Under-HD | ดูหนังออนไลน์ HD เต็มเรื่อง ครบจบ 2025</title>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, maximum-scale=3.0, minimum-scale=0.3">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎬</text></svg>" type="image/x-icon">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Kanit:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    
    <!-- Enhanced CSS -->
    <link rel="stylesheet" href="underhd-enhanced.css">
    
    <style>
        /* Base Styles */
        body {
            font-family: 'Kanit', sans-serif;
            background: #0a0a0a;
            color: white;
            margin: 0;
            padding: 0;
        }
        
        /* Demo Content */
        .demo-section {
            padding: 40px 20px;
            text-align: center;
            background: #0a0a0a;
        }
        
        .demo-title {
            font-size: 2rem;
            color: #ffd700;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #ffd700, #ff6b35);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .demo-text {
            color: #ccc;
            font-size: 1.1rem;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .features-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 40px;
            max-width: 1000px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .feature-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255, 215, 0, 0.2);
            transition: all 0.3s ease;
        }
        
        .feature-item:hover {
            background: rgba(255, 215, 0, 0.1);
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.2);
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .feature-title {
            color: #ffd700;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .feature-desc {
            color: #aaa;
            font-size: 0.9rem;
        }
    </style>
</head>

<body class="home">
    <div id="page">
        <header class="master-head">
            <div class="site-branding">
                <div class="header-info">
                    <section class="header-logo">
                        <a href="#">
                            <img src="https://via.placeholder.com/220x110/ffd700/1a1a2e?text=🎬+Under-HD" alt="Under-HD Logo">
                        </a>
                    </section>
                    <section class="header-description">
                        <h1>Under-HD ดูหนังออนไลน์ HD เต็มเรื่อง ครบจบ 2025</h1>
                        <div class="search-input">
                            <form role="search" method="GET" id="searchform" action="#">
                                <input type="text" value="" placeholder="ค้นหาหนัง, ซีรี่ย์, การ์ตูน..." name="s" id="input-ser">
                            </form>
                        </div>
                    </section>
                    <section class="contact">
                        <div class="hd-contact">
                            <a href="#">ติดต่อเรา</a>
                        </div>
                    </section>
                </div>
            </div>
            
            <!-- Main Navigation Menu -->
            <nav class="main-navigation">
                <div class="menu-menu-container">
                    <ul>
                        <li class="current-menu-item"><a href="#">หน้าแรก</a></li>
                        <li><a href="#">หนังใหม่</a></li>
                        <li><a href="#">หนังฮิต</a></li>
                        <li><a href="#">ซีรี่ย์</a></li>
                        <li><a href="#">การ์ตูน</a></li>
                        <li><a href="#">Netflix</a></li>
                        <li><a href="#">Disney+</a></li>
                        <li><a href="#">หมวดหมู่</a></li>
                    </ul>
                </div>
            </nav>
        </header>
        
        <!-- Demo Content -->
        <main class="demo-section">
            <div class="container-index">
                <h2 class="demo-title">🎬 Enhanced Modern Header</h2>
                <p class="demo-text">
                    ปรับปรุง CSS เดิมของคุณให้ทันสมัยขึ้น โดยใช้ class เดิมทั้งหมด<br>
                    ไม่ได้สร้าง class ใหม่ แค่เพิ่มเอฟเฟกต์และสีสันที่สวยงาม<br>
                    ลองเอาเมาส์ไปชี้ที่โลโก้, ช่องค้นหา, ปุ่มติดต่อ และเมนู
                </p>
                
                <div class="features-list">
                    <div class="feature-item">
                        <div class="feature-icon">🌈</div>
                        <div class="feature-title">Dynamic Gradient</div>
                        <div class="feature-desc">พื้นหลัง gradient เคลื่อนไหวสวยงาม</div>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">💎</div>
                        <div class="feature-title">Glass Morphism</div>
                        <div class="feature-desc">เอฟเฟกต์แก้วใสและ backdrop blur</div>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">⚡</div>
                        <div class="feature-title">Smooth Animations</div>
                        <div class="feature-desc">การเคลื่อนไหวที่นุ่มนวลทุกองค์ประกอบ</div>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">🎨</div>
                        <div class="feature-title">Golden Theme</div>
                        <div class="feature-desc">โทนสีทองที่หรูหราและทันสมัย</div>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">🔍</div>
                        <div class="feature-title">Enhanced Search</div>
                        <div class="feature-desc">ช่องค้นหาใหม่พร้อมเอฟเฟกต์สวยงาม</div>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">📱</div>
                        <div class="feature-title">Responsive</div>
                        <div class="feature-desc">รองรับทุกขนาดหน้าจออย่างสมบูรณ์</div>
                    </div>
                </div>
                
                <div style="margin-top: 40px; padding: 30px; background: rgba(255, 215, 0, 0.1); border-radius: 15px; border: 1px solid rgba(255, 215, 0, 0.3);">
                    <h3 style="color: #ffd700; margin-bottom: 15px;">🚀 วิธีนำไปใช้</h3>
                    <p style="color: #ccc; font-size: 1rem; line-height: 1.6;">
                        1. คัดลอก CSS จากไฟล์ <strong>underhd-enhanced.css</strong><br>
                        2. วางใน <strong>Appearance > Customize > Additional CSS</strong> ใน WordPress<br>
                        3. หรือเพิ่มใน <strong>style.css</strong> ของ theme<br>
                        4. CSS จะทำงานทันทีโดยใช้ class เดิมของคุณ
                    </p>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
