<?php
// This file contains the logic to capture the user's IP address, check for uniqueness, 
// and update the view count in the database. It includes functions to prevent duplicate views 
// within a specified timeframe.

include_once 'includes/db.php';
include_once 'includes/ip_utils.php';

function record_view($movie_id) {
    $ip_address = get_user_ip();
    $current_time = time();
    $time_frame = 3600; // 1 hour

    // Check if the IP address has already viewed this movie within the timeframe
    if (!has_viewed_recently($ip_address, $movie_id, $current_time, $time_frame)) {
        // Record the view in the database
        $db = db_connect();
        $stmt = $db->prepare("INSERT INTO movie_views (movie_id, ip_address, view_time) VALUES (?, ?, ?)");
        $stmt->execute([$movie_id, $ip_address, $current_time]);

        // Update the view count for the movie
        $stmt = $db->prepare("UPDATE movies SET view_count = view_count + 1 WHERE id = ?");
        $stmt->execute([$movie_id]);
    }
}

function has_viewed_recently($ip_address, $movie_id, $current_time, $time_frame) {
    $db = db_connect();
    $stmt = $db->prepare("SELECT COUNT(*) FROM movie_views WHERE ip_address = ? AND movie_id = ? AND view_time > ?");
    $stmt->execute([$ip_address, $movie_id, $current_time - $time_frame]);
    return $stmt->fetchColumn() > 0;
}
?>