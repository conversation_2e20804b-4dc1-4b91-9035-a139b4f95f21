/* Ultra Modern UnderHD Website Style - 2025 */
/* เพิ่ม !important เพื่อให้ CSS ทำงานแน่นอน */

/* Reset and Base */
* {
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
}

body {
    font-family: 'Kanit', sans-serif !important;
    background: #0a0a0a !important;
    color: white !important;
    overflow-x: hidden !important;
}

/* Modern Master Header - เพิ่ม !important */
.master-head {
    position: relative !important;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%) !important;
    background-size: 400% 400% !important;
    animation: gradientShift 15s ease infinite !important;
    padding: 20px 0 !important;
    box-shadow: 0 8px 40px rgba(0, 0, 0, 0.6) !important;
    border-bottom: 1px solid rgba(255, 215, 0, 0.2) !important;
    overflow: hidden !important;
}

/* Animated Background Effects */
.master-head::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 30%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(255, 69, 0, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(138, 43, 226, 0.05) 0%, transparent 50%);
    animation: floatingLights 20s ease-in-out infinite;
}

.master-head::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.03) 50%, transparent 70%);
    animation: shimmerEffect 12s linear infinite;
}

/* Site Branding Container */
.site-branding {
    position: relative;
    z-index: 10;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Info Layout */
.header-info {
    display: grid;
    grid-template-columns: auto 1fr auto;
    align-items: center;
    gap: 40px;
    min-height: 80px;
}

/* Logo Section */
.header-logo {
    position: relative;
}

.header-logo a {
    display: block;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    filter: drop-shadow(0 4px 12px rgba(255, 215, 0, 0.3));
}

.header-logo a:hover {
    transform: scale(1.1) rotate(2deg);
    filter: drop-shadow(0 8px 20px rgba(255, 215, 0, 0.5));
}

/* Header Description */
.header-description {
    text-align: center;
    position: relative;
}

.header-description h1 {
    font-size: clamp(2rem, 4vw, 3.5rem);
    font-weight: 800;
    margin-bottom: 25px;
    background: linear-gradient(45deg, #ffd700, #ff6b35, #f7931e, #ffd700);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: titleGlow 4s ease-in-out infinite;
    text-shadow: 0 0 30px rgba(255, 215, 0, 0.3);
    letter-spacing: 1px;
    position: relative;
}

.header-description h1::before {
    content: '🎬';
    position: absolute;
    left: -60px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 2.5rem;
    animation: iconFloat 3s ease-in-out infinite;
}

.header-description h1::after {
    content: '🎭';
    position: absolute;
    right: -60px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 2.5rem;
    animation: iconFloat 3s ease-in-out infinite reverse;
}

/* Modern Search Input */
.search-input {
    max-width: 500px;
    margin: 0 auto;
    position: relative;
}

.search-input form {
    position: relative;
    overflow: hidden;
    border-radius: 50px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 215, 0, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.search-input form:hover {
    border-color: rgba(255, 215, 0, 0.5);
    box-shadow: 0 12px 40px rgba(255, 215, 0, 0.2);
}

.search-input input[type="text"] {
    width: 100%;
    padding: 18px 60px 18px 25px;
    border: none;
    background: transparent;
    color: white;
    font-size: 16px;
    font-family: 'Kanit', sans-serif;
    outline: none;
    transition: all 0.3s ease;
}

.search-input input[type="text"]::placeholder {
    color: rgba(255, 255, 255, 0.7);
    font-weight: 300;
}

.search-input input[type="text"]:focus {
    background: rgba(255, 255, 255, 0.15);
}

.search-input form::before {
    content: '🔍';
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 20px;
    opacity: 0.8;
    animation: searchPulse 2s ease-in-out infinite;
}

/* Contact Section */
.contact {
    position: relative;
}

.hd-contact a {
    display: inline-flex;
    align-items: center;
    padding: 16px 32px;
    background: rgba(255, 215, 0, 0.15);
    color: #ffd700;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 15px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 215, 0, 0.4);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 4px 20px rgba(255, 215, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.hd-contact a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.3), transparent);
    transition: left 0.6s;
}

.hd-contact a:hover::before {
    left: 100%;
}

.hd-contact a:hover {
    background: rgba(255, 215, 0, 0.25);
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 30px rgba(255, 215, 0, 0.4);
    color: white;
}

/* Modern Movie Cards (for hover effects) */
.movie-item,
.post-item,
.film-item {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.movie-item:hover,
.post-item:hover,
.film-item:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 60px rgba(255, 215, 0, 0.3);
    border-color: rgba(255, 215, 0, 0.5);
}

.movie-item::before,
.post-item::before,
.film-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(255, 215, 0, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.movie-item:hover::before,
.post-item:hover::before,
.film-item:hover::before {
    opacity: 1;
}

/* Movie Poster Effects */
.movie-poster,
.post-thumbnail,
.film-poster {
    position: relative;
    overflow: hidden;
    border-radius: 12px;
    transition: all 0.4s ease;
}

.movie-poster img,
.post-thumbnail img,
.film-poster img {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    filter: brightness(0.9) contrast(1.1);
}

.movie-item:hover .movie-poster img,
.post-item:hover .post-thumbnail img,
.film-item:hover .film-poster img {
    transform: scale(1.1);
    filter: brightness(1.1) contrast(1.2);
}

/* Modern Menu Styles */
.main-menu,
.header-menu,
.navigation-menu {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(20px);
    border-radius: 50px;
    padding: 8px;
    margin-top: 20px;
    border: 1px solid rgba(255, 215, 0, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.main-menu ul,
.header-menu ul,
.navigation-menu ul {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    list-style: none;
    flex-wrap: wrap;
}

.main-menu li,
.header-menu li,
.navigation-menu li {
    position: relative;
}

.main-menu a,
.header-menu a,
.navigation-menu a {
    display: block;
    padding: 12px 24px;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    border-radius: 25px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
}

.main-menu a::before,
.header-menu a::before,
.navigation-menu a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.2), transparent);
    transition: left 0.5s;
}

.main-menu a:hover::before,
.header-menu a:hover::before,
.navigation-menu a:hover::before {
    left: 100%;
}

.main-menu a:hover,
.header-menu a:hover,
.navigation-menu a:hover {
    background: rgba(255, 215, 0, 0.2);
    color: #ffd700;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.3);
}

/* Active Menu Item */
.main-menu .current-menu-item a,
.header-menu .current-menu-item a,
.navigation-menu .current-menu-item a {
    background: rgba(255, 215, 0, 0.25);
    color: #ffd700;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

/* Animations */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes floatingLights {
    0%, 100% { opacity: 1; transform: translateY(0px); }
    50% { opacity: 0.7; transform: translateY(-10px); }
}

@keyframes shimmerEffect {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

@keyframes titleGlow {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes iconFloat {
    0%, 100% { transform: translateY(-50%) scale(1); }
    50% { transform: translateY(-50%) scale(1.2); }
}

@keyframes searchPulse {
    0%, 100% { opacity: 0.8; transform: translateY(-50%) scale(1); }
    50% { opacity: 1; transform: translateY(-50%) scale(1.1); }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .header-info {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 25px;
    }
    
    .header-description h1::before,
    .header-description h1::after {
        display: none;
    }
}

@media (max-width: 768px) {
    .site-branding {
        padding: 0 15px;
    }
    
    .header-description h1 {
        font-size: 2.2rem;
        margin-bottom: 20px;
    }
    
    .search-input input[type="text"] {
        padding: 15px 50px 15px 20px;
        font-size: 15px;
    }
    
    .hd-contact a {
        padding: 14px 28px;
        font-size: 14px;
    }
    
    .main-menu ul,
    .header-menu ul,
    .navigation-menu ul {
        flex-direction: column;
        gap: 8px;
    }
    
    .main-menu a,
    .header-menu a,
    .navigation-menu a {
        padding: 10px 20px;
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .master-head {
        padding: 15px 0;
    }
    
    .header-info {
        gap: 20px;
    }
    
    .header-description h1 {
        font-size: 1.8rem;
    }
    
    .search-input {
        max-width: 100%;
    }
}
