/* Ultra Modern Movie Header - 2024 Design */

/* Reset and Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Kanit', sans-serif;
    overflow-x: hidden;
}

/* Modern Header Container */
.master-head {
    position: relative;
    background: linear-gradient(135deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
    background-size: 300% 300%;
    animation: gradientWave 8s ease infinite;
    min-height: 120px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
}

/* Animated Background Effects */
.master-head::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    animation: floatBubbles 12s ease-in-out infinite;
}

.master-head::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: shimmer 6s linear infinite;
}

/* Site Branding Container */
.site-branding {
    position: relative;
    z-index: 10;
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Info Layout */
.header-info {
    display: grid;
    grid-template-columns: auto 1fr auto;
    align-items: center;
    gap: 30px;
    min-height: 80px;
}

/* Logo Section */
.header-logo {
    position: relative;
}

.header-logo a {
    display: block;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.header-logo a:hover {
    transform: scale(1.1) rotate(2deg);
    filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.3));
}

/* Header Description */
.header-description {
    text-align: center;
    color: white;
}

.header-description h1 {
    font-size: clamp(1.8rem, 4vw, 3.2rem);
    font-weight: 800;
    margin-bottom: 20px;
    text-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    background: linear-gradient(45deg, #ffffff, #f8f9fa, #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: textGlow 4s ease-in-out infinite alternate;
    letter-spacing: 1px;
}

/* Modern Search Input */
.search-input {
    max-width: 450px;
    margin: 0 auto;
    position: relative;
}

.search-input form {
    position: relative;
    overflow: hidden;
    border-radius: 50px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.search-input input[type="text"] {
    width: 100%;
    padding: 16px 24px;
    border: none;
    background: transparent;
    color: white;
    font-size: 16px;
    font-family: 'Kanit', sans-serif;
    outline: none;
    transition: all 0.3s ease;
}

.search-input input[type="text"]::placeholder {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 300;
}

.search-input input[type="text"]:focus {
    background: rgba(255, 255, 255, 0.25);
}

.search-input form::before {
    content: '🔍';
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 18px;
    opacity: 0.7;
}

/* Contact Button */
.contact {
    position: relative;
}

.hd-contact a {
    display: inline-flex;
    align-items: center;
    padding: 14px 28px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 15px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.hd-contact a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.hd-contact a:hover::before {
    left: 100%;
}

.hd-contact a:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
}

/* Animations */
@keyframes gradientWave {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes floatBubbles {
    0%, 100% { transform: translateY(0px) scale(1); }
    50% { transform: translateY(-20px) scale(1.1); }
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

@keyframes textGlow {
    0% { text-shadow: 0 4px 12px rgba(0, 0, 0, 0.3); }
    100% { text-shadow: 0 4px 20px rgba(255, 255, 255, 0.4), 0 0 30px rgba(255, 255, 255, 0.2); }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .header-info {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 20px;
    }
    
    .search-input {
        max-width: 100%;
    }
}

@media (max-width: 768px) {
    .site-branding {
        padding: 15px;
    }
    
    .header-description h1 {
        font-size: 2rem;
        margin-bottom: 15px;
    }
    
    .search-input input[type="text"] {
        padding: 14px 20px;
        font-size: 15px;
    }
    
    .hd-contact a {
        padding: 12px 24px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .master-head {
        min-height: 100px;
    }
    
    .header-info {
        gap: 15px;
    }
    
    .header-description h1 {
        font-size: 1.6rem;
    }
}

/* View Counter Styles */
.view-counter {
    font-size: 16px;
    color: #333;
    margin-top: 10px;
    padding: 12px 20px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #4ecdc4;
}

.view-counter span {
    font-weight: bold;
    color: #2c3e50;
}

.view-counter .icon {
    margin-right: 8px;
    font-size: 18px;
}
